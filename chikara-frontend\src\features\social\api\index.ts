// Friend hooks - ORPC versions
export { default as useGetFriendList } from "./useGetFriendList.orpc";
export { default as useGetFriendRequests } from "./useGetFriendRequests.orpc";
export { default as useSendFriendRequest } from "./useSendFriendRequest.orpc";
export { default as useRespondToFriendRequest } from "./useRespondToFriendRequest.orpc";
export { default as useRemoveFriend } from "./useRemoveFriend.orpc";
export { default as useUpdateFriendNote } from "./useUpdateFriendNote.orpc";

// Profile/status hooks - ORPC versions
export { default as useUpdateStatusMessage } from "./useUpdateStatusMessage.orpc";
export { default as useTogglePrivacySettings } from "./useTogglePrivacySettings.orpc";

// Rival hooks - ORPC versions
export { default as useGetRivalsList } from "./useGetRivalsList.orpc";
export { default as useAddRival } from "./useAddRival.orpc";
export { default as useRemoveRival } from "./useRemoveRival.orpc";
export { default as useUpdateRivalNote } from "./useUpdateRivalNote.orpc";
