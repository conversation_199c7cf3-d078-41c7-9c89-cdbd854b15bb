import { z } from "zod";
import { isLoggedInAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import SocialController from "./social.controller.js";
import socialValidation from "./social.validation.js";

export const socialRouter = {
    // Friend routes
    getFriendsList: isLoggedInAuth.handler(async ({ context }) => {
        const response = await SocialController.getFriendsList(context.user.id);
        return handleResponse(response);
    }),

    getFriendRequests: isLoggedInAuth.handler(async ({ context }) => {
        const response = await SocialController.getFriendRequests(context.user.id);
        return handleResponse(response);
    }),

    sendFriendRequest: isLoggedInAuth
        .input(socialValidation.sendFriendRequestSchema)
        .handler(async ({ input, context }) => {
            const response = await SocialController.sendFriendRequest(context.user.id, input.userId);
            return handleResponse(response);
        }),

    respondToFriendRequest: isLoggedInAuth
        .input(socialValidation.respondToFriendRequestSchema)
        .handler(async ({ input, context }) => {
            const response = await SocialController.respondToFriendRequest(
                context.user.id,
                input.requestId,
                input.accept
            );
            return handleResponse({data: response});
        }),

    removeFriend: isLoggedInAuth
        .input(z.object({ friendId: z.number().int().positive() }))
        .handler(async ({ input, context }) => {
            const response = await SocialController.removeFriend(context.user.id, input.friendId);
            return handleResponse(response);
        }),

    updateFriendNote: isLoggedInAuth
        .input(socialValidation.updateFriendNoteSchema)
        .handler(async ({ input, context }) => {
            const response = await SocialController.updateFriendNote(
                context.user.id,
                input.friendId,
                input.note
            );
            return handleResponse(response);
        }),

    updateStatusMessage: isLoggedInAuth
        .input(socialValidation.updateStatusMessageSchema)
        .handler(async ({ input, context }) => {
            const response = await SocialController.updateStatusMessage(context.user.id, input.message);
            return handleResponse(response);
        }),

    updatePrivacySettings: isLoggedInAuth
        .input(socialValidation.togglePrivacySettingSchema)
        .handler(async ({ input, context }) => {
            const response = await SocialController.updatePrivacySettings(
                context.user.id,
                input.showLastOnline
            );
            return handleResponse(response);
        }),

    // Rival routes
    getRivalsList: isLoggedInAuth.handler(async ({ context }) => {
        const response = await SocialController.getRivalsList(context.user.id);
        return handleResponse(response);
    }),

    addRival: isLoggedInAuth
        .input(socialValidation.addRivalSchema)
        .handler(async ({ input, context }) => {
            const response = await SocialController.addRival(context.user.id, input.userId);
            return handleResponse(response);
        }),

    removeRival: isLoggedInAuth
        .input(z.object({ rivalId: z.number().int().positive() }))
        .handler(async ({ input, context }) => {
            const response = await SocialController.removeRival(context.user.id, input.rivalId);
            return handleResponse(response);
        }),

    updateRivalNote: isLoggedInAuth
        .input(socialValidation.updateRivalNoteSchema)
        .handler(async ({ input, context }) => {
            const response = await SocialController.updateRivalNote(
                context.user.id,
                input.rivalId,
                input.note
            );
            return handleResponse(response);
        }),
};
